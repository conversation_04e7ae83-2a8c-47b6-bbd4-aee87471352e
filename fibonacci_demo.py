def fibonacci(n):
    """Calculate fibonacci number using recursion"""
    if n <= 1:
        return n
    return fibonacci(n-1) + <PERSON><PERSON><PERSON><PERSON>(n-2)

def fibonacci_iterative(n):
    """Calculate fibonacci number using iteration"""
    if n <= 1:
        return n
    
    a, b = 0, 1
    for _ in range(2, n + 1):
        a, b = b, a + b
    return b

if __name__ == "__main__":
    # Test both implementations
    for i in range(10):
        print(f"fib({i}) = {fibonacci(i)} (recursive)")
        print(f"fib({i}) = {fibonacci_iterative(i)} (iterative)")
