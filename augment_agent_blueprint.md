# Blueprint for an Augment-like Coding Agent

This document provides step-by-step instructions for building an Augment-like coding agent, utilizing Augment's world-leading context engine and developer codebase integrations.

## Phase 1: Human Design (Conceptualization and Specification)

### 1. Set Up the Agentic Coding Environment

**Utilize Augment's Infrastructure:**
- Foundation requires access to Augment's context engine and codebase integrations
- Leverage Augment's proprietary retrieval/embedding model for real-time codebase indexing
- Use Augment's file operations API as the core interface for code manipulation
- Set up workspace with repository root for proper path resolution

**Environment Configuration:**
- Establish workspace directory (e.g., `c:\Users\<USER>\Documents\augment-projects\[project]`)
- Configure repository root to match workspace for relative path resolution
- Ensure access to shell environment (PowerShell on Windows, appropriate shell on other platforms)

### 2. Extract and Analyze Augment's Core Capabilities

**Study Augment's Instruction Set:**
Based on the Augment instruction file, the agent should understand these core capabilities:

**File Operations:**
- **view**: Primary tool for reading files/directories with optional line ranges and regex search
- **save-file**: Create new files only (with 300-line limit reminder)
- **str-replace-editor**: Primary editing tool for existing files (with 150-line limit reminder)
- **remove-files**: Safe file deletion with undo capability

**Information Gathering:**
- **codebase-retrieval**: Get current codebase context using natural language queries
- **git-commit-retrieval**: Access commit history for implementation patterns

**Process Management:**
- **launch-process**: Execute shell commands with working directory and timeout control

### 3. Define the Agent's Action Space

**Core Actions (Educational/Simplified Version):**
- Reading files and directories
- Creating new files
- Editing existing files using string replacement or insertion
- Searching codebase using natural language queries
- Exploring git history for context
- Running shell commands for testing and package management
- File deletion (with safety features)

**Advanced Actions (Full Implementation):**
- Semantic codebase search via proprietary retrieval model
- Real-time code context gathering
- Package manager integration (npm, pip, cargo, etc.)
- Test execution and iteration
- Task management for complex workflows

### 4. Design the Agent Flow Architecture

**Central Agent Node Design:**
```
User Request → Context Gathering → Action Planning → Execution → Verification → Response
```

**Context Management Strategy:**
- Always call `codebase-retrieval` before making edits
- Request specific, detailed information about code to be modified
- Include all symbols, classes, methods, and properties in single detailed calls
- Maintain focus on relevant context only to avoid "lost in the middle" problem

**Action Decomposition for Complex Tasks:**
```
Complex Edit Request →
├── Information Gathering (codebase-retrieval)
├── Planning Phase (break down into subtasks)  
├── Implementation Phase
│   ├── File Reading (view)
│   ├── String Replacement/Insertion (str-replace-editor)
│   └── Verification (view changes)
└── Testing/Validation Phase (launch-process)
```

### 5. Build Utility Functions Integration

**File Operation Utilities:**
```python
# View files/directories
def view_code(path, line_range=None, search_pattern=None):
    # Implementation using view tool
    
# Edit existing files
def edit_file(path, replacements, insertions):
    # Implementation using str-replace-editor
    
# Create new files  
def create_file(path, content):
    # Implementation using save-file with safety checks
```

**Context Gathering Utilities:**
```python
# Get codebase context
def gather_context(information_request):
    # Implementation using codebase-retrieval
    
# Get git history context
def get_historical_context(information_request):
    # Implementation using git-commit-retrieval
```

**Process Management Utilities:**
```python
# Execute commands safely
def run_command(command, working_dir, wait=True, timeout=30):
    # Implementation using launch-process
```

### 6. Design Node Communication Schema

**Shared Storage Structure:**
```json
{
  "user_query": "Original user request",
  "context_data": {
    "codebase_info": "Retrieved codebase context",
    "git_history": "Relevant historical context",
    "file_structure": "Current file/directory structure"
  },
  "action_history": [
    {"action": "view", "path": "...", "result": "..."},
    {"action": "edit", "path": "...", "changes": "..."}
  ],
  "current_task_state": "planning|executing|verifying|complete",
  "task_breakdown": [
    {"task": "...", "status": "[ ]|[/]|[x]|[-]", "subtasks": [...]}
  ],
  "final_response": "Response to user"
}
```

**Node Processing Pattern:**
Each node follows three steps:
1. **Pre-processing**: Read from shared storage, validate inputs
2. **Execution**: Core work (API calls, file operations, command execution)
3. **Post-processing**: Update shared storage, log results

## Phase 2: AI Implementation

### 1. Context-First Implementation Pattern

**Before Any Edit Operations:**
```python
# CRITICAL: Always gather context first
context = await codebase_retrieval({
    "information_request": f"Detailed information about {specific_code_elements} including all related symbols, classes, methods, and properties"
})

# Analyze context before proceeding
file_structure = await view({"path": target_directory, "type": "directory"})
current_code = await view({"path": target_file, "type": "file"})
```

### 2. Conservative Implementation Approach

**Focus Principles:**
- Only implement what user explicitly requests
- Respect existing codebase patterns and conventions
- Ask permission before: committing, pushing, merging, installing dependencies, deploying
- Use package managers instead of manual config file edits

**Error Recovery Strategy:**
- If stuck in cycles, ask user for clarification
- Don't repeatedly call same tools without progress
- Provide clear status updates during complex operations

### 3. Testing Integration Pattern

**Test-Driven Workflow:**
```python
# After code changes
def suggest_and_run_tests():
    # Suggest writing/updating tests
    # Implement test code
    # Run tests using launch-process
    # Iterate until tests pass
    # Report results to user
```

### 4. Code Display Formatting

**Always use Augment's display format:**
```xml
<augment_code_snippet path="file/path.ext" mode="EXCERPT">
````language
code here (max 10 lines)
````
</augment_code_snippet>
```

### 5. Task Management for Complex Operations

**When work is complex:**
```python
# Use task management tools
await add_tasks({"tasks": structured_task_breakdown})
await update_tasks({"updates": progress_updates})
# Keep user informed of progress
```

## Phase 3: Agent Execution Command

### Final Implementation Command

Once the design is complete, provide this instruction to the AI agent:

```
"Start agentic coding implementation based on this design document. 

Key requirements:
1. Always call codebase-retrieval before making any edits
2. Use str-replace-editor as primary editing tool with proper line number tracking
3. Follow conservative approach - only implement what's requested
4. Use proper code display formatting with augment_code_snippet tags
5. Implement comprehensive logging for progress tracking
6. Follow Augment's workflow rules and safety patterns
7. Test implementations thoroughly before completion

Begin implementation and provide detailed progress updates."
```

## Key Differences from Cursor-like Agents

**Augment-Specific Features:**
- Real-time codebase indexing vs. static analysis
- Proprietary retrieval/embedding model for semantic search
- Integrated git history access
- Built-in task management system
- Conservative, permission-based approach to system changes
- Advanced context engine for better code understanding

**Safety and Reliability:**
- File operations include undo capabilities
- Process execution with timeout controls
- Context gathering is mandatory before edits
- Progressive disclosure of complex operations
- Built-in error recovery patterns

This blueprint creates a robust, context-aware coding agent that leverages Augment's advanced capabilities while maintaining safety and user control throughout the development process.