#!/usr/bin/env python3
"""
AIIDE - Augment-Inspired Intelligent Development Environment
An Augment-like coding agent following the blueprint architecture.

This agent implements the core patterns from Augment Agent:
- Context-first approach with mandatory codebase-retrieval before edits
- Conservative implementation respecting existing codebase patterns
- Comprehensive file operations with safety features
- Task management for complex workflows
- Test-driven development integration
"""

import json
import logging
import os
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from enum import Enum


class TaskState(Enum):
    """Task states following Augment's task management system"""
    NOT_STARTED = "[ ]"
    IN_PROGRESS = "[/]"
    CANCELLED = "[-]"
    COMPLETE = "[x]"


class AgentState(Enum):
    """Current state of the agent processing"""
    IDLE = "idle"
    PLANNING = "planning"
    EXECUTING = "executing"
    VERIFYING = "verifying"
    COMPLETE = "complete"
    ERROR = "error"


@dataclass
class Task:
    """Task structure for complex workflow management"""
    task_id: str
    name: str
    description: str
    state: TaskState = TaskState.NOT_STARTED
    parent_id: Optional[str] = None
    subtasks: List[str] = None
    
    def __post_init__(self):
        if self.subtasks is None:
            self.subtasks = []


@dataclass
class ActionRecord:
    """Record of actions taken by the agent"""
    action: str
    path: Optional[str] = None
    parameters: Dict[str, Any] = None
    result: Optional[str] = None
    timestamp: Optional[str] = None
    
    def __post_init__(self):
        if self.parameters is None:
            self.parameters = {}
        if self.timestamp is None:
            import datetime
            self.timestamp = datetime.datetime.now().isoformat()


@dataclass
class SharedStorage:
    """Shared storage structure for node communication following blueprint"""
    user_query: str
    context_data: Dict[str, Any]
    action_history: List[ActionRecord]
    current_task_state: AgentState
    task_breakdown: List[Task]
    final_response: str = ""
    
    def __post_init__(self):
        if not self.context_data:
            self.context_data = {
                "codebase_info": "",
                "git_history": "",
                "file_structure": ""
            }
        if not self.action_history:
            self.action_history = []
        if not self.task_breakdown:
            self.task_breakdown = []


class AIIDEAgent:
    """
    AIIDE - Augment-Inspired Intelligent Development Environment
    
    Core agent implementing Augment's patterns:
    - Context gathering before edits
    - Conservative approach
    - Safety-first file operations
    - Task management integration
    """
    
    def __init__(self, workspace_root: str):
        self.workspace_root = workspace_root
        self.storage = None
        self.logger = self._setup_logging()

        # Load Augment instructions
        self.augment_instructions = self._load_augment_instructions()

        # Initialize utility classes
        self.file_ops = FileOperationUtilities(self)
        self.context_ops = ContextGatheringUtilities(self)
        self.process_ops = ProcessManagementUtilities(self)
        
    def _setup_logging(self) -> logging.Logger:
        """Set up comprehensive logging for progress tracking"""
        logger = logging.getLogger("AIIDE")
        logger.setLevel(logging.INFO)
        
        # Create console handler
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        
        return logger
        
    def _load_augment_instructions(self) -> Dict[str, Any]:
        """Load Augment instructions from augment_instruction.txt"""
        instructions_path = os.path.join(self.workspace_root, "augment_instruction.txt")
        try:
            with open(instructions_path, 'r', encoding='utf-8') as f:
                content = f.read()
            self.logger.info("Loaded Augment instructions successfully")
            return {"content": content, "loaded": True}
        except FileNotFoundError:
            self.logger.warning("augment_instruction.txt not found")
            return {"content": "", "loaded": False}
    
    def initialize_session(self, user_query: str) -> SharedStorage:
        """Initialize a new coding session with user query"""
        self.logger.info(f"Initializing AIIDE session: {user_query[:100]}...")
        
        self.storage = SharedStorage(
            user_query=user_query,
            context_data={},
            action_history=[],
            current_task_state=AgentState.PLANNING,
            task_breakdown=[]
        )
        
        # Log initialization
        init_action = ActionRecord(
            action="initialize_session",
            parameters={"user_query": user_query}
        )
        self.storage.action_history.append(init_action)
        
        return self.storage
    
    def process_request(self, user_query: str) -> str:
        """
        Main entry point for processing user requests
        Follows the central agent node design:
        User Request → Context Gathering → Action Planning → Execution → Verification → Response
        """
        try:
            # Initialize session
            self.initialize_session(user_query)
            
            # Phase 1: Context Gathering (CRITICAL - always first)
            self.logger.info("Phase 1: Context Gathering")
            self._gather_context()
            
            # Phase 2: Action Planning
            self.logger.info("Phase 2: Action Planning")
            self.storage.current_task_state = AgentState.PLANNING
            self._plan_actions()
            
            # Phase 3: Execution
            self.logger.info("Phase 3: Execution")
            self.storage.current_task_state = AgentState.EXECUTING
            self._execute_actions()
            
            # Phase 4: Verification
            self.logger.info("Phase 4: Verification")
            self.storage.current_task_state = AgentState.VERIFYING
            self._verify_results()
            
            # Phase 5: Response
            self.logger.info("Phase 5: Response Generation")
            self.storage.current_task_state = AgentState.COMPLETE
            response = self._generate_response()
            
            return response
            
        except Exception as e:
            self.logger.error(f"Error processing request: {str(e)}")
            self.storage.current_task_state = AgentState.ERROR
            return f"AIIDE encountered an error: {str(e)}"
    
    def _gather_context(self):
        """Gather context before any operations - CRITICAL step"""
        self.logger.info("Gathering context using Augment's context engine...")

        # Use the context gathering utilities
        context_request = f"Analyze codebase for: {self.storage.user_query}"
        comprehensive_context = self.context_ops.gather_comprehensive_context(context_request)

        # Update storage with gathered context
        self.storage.context_data.update(comprehensive_context)
    
    def _plan_actions(self):
        """Plan actions based on gathered context"""
        self.logger.info("Planning actions based on context...")

        # Analyze the user query and context to plan actions
        query = self.storage.user_query.lower()
        context = self.storage.context_data

        # Create action plan based on query type
        if "create" in query and "file" in query:
            self._plan_file_creation()
        elif "edit" in query or "modify" in query:
            self._plan_file_editing()
        elif "test" in query:
            self._plan_testing()
        elif "install" in query or "package" in query:
            self._plan_package_management()
        else:
            self._plan_general_coding_task()

    def _plan_file_creation(self):
        """Plan file creation actions"""
        self.logger.info("Planning file creation...")
        # Add file creation task to breakdown
        task = Task(
            task_id="create_file_task",
            name="Create File",
            description=f"Create file as requested: {self.storage.user_query}",
            state=TaskState.NOT_STARTED
        )
        self.storage.task_breakdown.append(task)

    def _plan_file_editing(self):
        """Plan file editing actions"""
        self.logger.info("Planning file editing...")
        # Add editing task to breakdown
        task = Task(
            task_id="edit_file_task",
            name="Edit File",
            description=f"Edit file as requested: {self.storage.user_query}",
            state=TaskState.NOT_STARTED
        )
        self.storage.task_breakdown.append(task)

    def _plan_testing(self):
        """Plan testing actions"""
        self.logger.info("Planning testing...")
        task = Task(
            task_id="run_tests_task",
            name="Run Tests",
            description=f"Execute tests as requested: {self.storage.user_query}",
            state=TaskState.NOT_STARTED
        )
        self.storage.task_breakdown.append(task)

    def _plan_package_management(self):
        """Plan package management actions"""
        self.logger.info("Planning package management...")
        task = Task(
            task_id="package_mgmt_task",
            name="Package Management",
            description=f"Manage packages as requested: {self.storage.user_query}",
            state=TaskState.NOT_STARTED
        )
        self.storage.task_breakdown.append(task)

    def _plan_general_coding_task(self):
        """Plan general coding task"""
        self.logger.info("Planning general coding task...")
        task = Task(
            task_id="general_coding_task",
            name="General Coding Task",
            description=f"Execute coding task: {self.storage.user_query}",
            state=TaskState.NOT_STARTED
        )
        self.storage.task_breakdown.append(task)

    def _execute_actions(self):
        """Execute planned actions"""
        self.logger.info("Executing planned actions...")

        # Execute each task in the breakdown
        for task in self.storage.task_breakdown:
            if task.state == TaskState.NOT_STARTED:
                self.logger.info(f"Executing task: {task.name}")
                task.state = TaskState.IN_PROGRESS

                # Execute based on task type
                if "create_file" in task.task_id:
                    self._execute_file_creation(task)
                elif "edit_file" in task.task_id:
                    self._execute_file_editing(task)
                elif "run_tests" in task.task_id:
                    self._execute_testing(task)
                elif "package_mgmt" in task.task_id:
                    self._execute_package_management(task)
                else:
                    self._execute_general_task(task)

                task.state = TaskState.COMPLETE

    def _execute_file_creation(self, task: Task):
        """Execute file creation"""
        # Simple example - create a Python function file
        content = '''def example_function():
    """Example function created by AIIDE"""
    return "Hello from AIIDE!"

if __name__ == "__main__":
    print(example_function())
'''
        result = self.file_ops.create_file("example_function.py", content)
        self.logger.info(f"File creation result: {result}")

    def _execute_file_editing(self, task: Task):
        """Execute file editing"""
        # Example editing operation
        self.logger.info("File editing would be implemented here")

    def _execute_testing(self, task: Task):
        """Execute testing"""
        result = self.process_ops.run_tests()
        self.logger.info(f"Test execution result: {result}")

    def _execute_package_management(self, task: Task):
        """Execute package management"""
        self.logger.info("Package management would be implemented here")

    def _execute_general_task(self, task: Task):
        """Execute general coding task"""
        self.logger.info("General task execution would be implemented here")

    def _verify_results(self):
        """Verify results of executed actions"""
        self.logger.info("Verifying results...")

        # Check if all tasks completed successfully
        completed_tasks = [t for t in self.storage.task_breakdown if t.state == TaskState.COMPLETE]
        total_tasks = len(self.storage.task_breakdown)

        self.logger.info(f"Completed {len(completed_tasks)}/{total_tasks} tasks")

        # Verify file operations if any
        for action in self.storage.action_history:
            if action.action in ["create_file", "edit_file"]:
                self._verify_file_operation(action)
    
    def _verify_file_operation(self, action: ActionRecord):
        """Verify a file operation was successful"""
        if action.path:
            full_path = os.path.join(self.workspace_root, action.path)
            if os.path.exists(full_path):
                self.logger.info(f"Verified: {action.path} exists")
            else:
                self.logger.warning(f"Verification failed: {action.path} not found")

    def _generate_response(self) -> str:
        """Generate final response to user"""
        # Create comprehensive response
        completed_tasks = [t for t in self.storage.task_breakdown if t.state == TaskState.COMPLETE]
        failed_tasks = [t for t in self.storage.task_breakdown if t.state != TaskState.COMPLETE]

        response_parts = [
            f"AIIDE completed processing: {self.storage.user_query}",
            f"",
            f"Tasks completed: {len(completed_tasks)}/{len(self.storage.task_breakdown)}",
        ]

        if completed_tasks:
            response_parts.append("✓ Completed tasks:")
            for task in completed_tasks:
                response_parts.append(f"  - {task.name}: {task.description}")

        if failed_tasks:
            response_parts.append("⚠ Incomplete tasks:")
            for task in failed_tasks:
                response_parts.append(f"  - {task.name}: {task.description}")

        # Add action summary
        file_actions = [a for a in self.storage.action_history if a.action in ["create_file", "edit_file", "view"]]
        if file_actions:
            response_parts.append("")
            response_parts.append("File operations performed:")
            for action in file_actions:
                response_parts.append(f"  - {action.action}: {action.path or 'N/A'}")

        self.storage.final_response = "\n".join(response_parts)
        return self.storage.final_response

    def get_status(self) -> Dict[str, Any]:
        """Get current status of the agent and its operations"""
        if not self.storage:
            return {"status": "not_initialized"}

        return {
            "status": self.storage.current_task_state.value,
            "user_query": self.storage.user_query,
            "tasks": [
                {
                    "id": task.task_id,
                    "name": task.name,
                    "state": task.state.value,
                    "description": task.description
                }
                for task in self.storage.task_breakdown
            ],
            "actions_performed": len(self.storage.action_history),
            "context_gathered": bool(self.storage.context_data.get("context_gathering_complete"))
        }

    def display_code_snippet(self, file_path: str, content: str, max_lines: int = 10) -> str:
        """
        Display code using Augment's format specification
        Always use augment_code_snippet tags as per instructions
        """
        lines = content.split('\n')
        if len(lines) > max_lines:
            lines = lines[:max_lines]
            content = '\n'.join(lines) + '\n...'
        else:
            content = '\n'.join(lines)

        # Detect language from file extension
        ext_to_lang = {
            '.py': 'python',
            '.js': 'javascript',
            '.ts': 'typescript',
            '.java': 'java',
            '.cpp': 'cpp',
            '.c': 'c',
            '.h': 'c',
            '.md': 'markdown'
        }

        ext = os.path.splitext(file_path)[1]
        language = ext_to_lang.get(ext, 'text')

        return f'''<augment_code_snippet path="{file_path}" mode="EXCERPT">
````{language}
{content}
````
</augment_code_snippet>'''


class FileOperationUtilities:
    """
    File Operation Utilities following Augment's API specifications
    Implements view, edit, create file operations with safety features
    """

    def __init__(self, agent: AIIDEAgent):
        self.agent = agent
        self.logger = agent.logger

    def view_code(self, path: str, line_range: Optional[List[int]] = None,
                  search_pattern: Optional[str] = None) -> Dict[str, Any]:
        """
        View files/directories using Augment's view tool specification

        Args:
            path: File/directory path relative to workspace root
            line_range: Optional [start_line, end_line] for specific ranges
            search_pattern: Optional regex pattern to search within files

        Returns:
            Dict containing view results and metadata
        """
        self.logger.info(f"Viewing: {path}")

        # Record action
        action = ActionRecord(
            action="view",
            path=path,
            parameters={
                "line_range": line_range,
                "search_pattern": search_pattern
            }
        )

        try:
            full_path = os.path.join(self.agent.workspace_root, path)

            if os.path.isdir(full_path):
                result = self._view_directory(full_path)
                action.result = f"Directory listing: {len(result.get('files', []))} items"
            elif os.path.isfile(full_path):
                result = self._view_file(full_path, line_range, search_pattern)
                action.result = f"File content: {result.get('line_count', 0)} lines"
            else:
                result = {"error": f"Path not found: {path}"}
                action.result = "Path not found"

            # Only append to action history if storage is initialized
            if self.agent.storage:
                self.agent.storage.action_history.append(action)
            return result

        except Exception as e:
            error_msg = f"Error viewing {path}: {str(e)}"
            self.logger.error(error_msg)
            action.result = error_msg
            # Only append to action history if storage is initialized
            if self.agent.storage:
                self.agent.storage.action_history.append(action)
            return {"error": error_msg}

    def _view_directory(self, full_path: str) -> Dict[str, Any]:
        """View directory contents up to 2 levels deep"""
        result = {"type": "directory", "path": full_path, "files": []}

        try:
            for root, dirs, files in os.walk(full_path):
                level = root.replace(full_path, '').count(os.sep)
                if level >= 2:
                    dirs[:] = []  # Don't go deeper than 2 levels
                    continue

                indent = ' ' * 2 * level
                result["files"].append(f"{indent}{os.path.basename(root)}/")

                subindent = ' ' * 2 * (level + 1)
                for file in files:
                    if not file.startswith('.'):  # Exclude hidden files
                        result["files"].append(f"{subindent}{file}")

        except Exception as e:
            result["error"] = str(e)

        return result

    def _view_file(self, full_path: str, line_range: Optional[List[int]] = None,
                   search_pattern: Optional[str] = None) -> Dict[str, Any]:
        """View file contents with optional line range and search"""
        result = {"type": "file", "path": full_path}

        try:
            with open(full_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            result["line_count"] = len(lines)

            # Apply line range if specified
            if line_range:
                start, end = line_range
                start = max(1, start) - 1  # Convert to 0-based
                end = min(len(lines), end) if end != -1 else len(lines)
                lines = lines[start:end]
                result["range_applied"] = [start + 1, end]

            # Apply search pattern if specified
            if search_pattern:
                import re
                pattern = re.compile(search_pattern)
                matching_lines = []
                for i, line in enumerate(lines):
                    if pattern.search(line):
                        matching_lines.append((i + 1, line.rstrip()))
                result["matches"] = matching_lines
                result["content"] = matching_lines
            else:
                # Return numbered lines
                numbered_lines = []
                start_num = line_range[0] if line_range else 1
                for i, line in enumerate(lines):
                    numbered_lines.append(f"{start_num + i:4d}  {line.rstrip()}")
                result["content"] = numbered_lines

        except Exception as e:
            result["error"] = str(e)

        return result

    def edit_file(self, path: str, replacements: List[Dict[str, Any]] = None,
                  insertions: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Edit existing files using str-replace-editor specification

        Args:
            path: File path relative to workspace root
            replacements: List of replacement operations
            insertions: List of insertion operations

        Returns:
            Dict containing edit results
        """
        self.logger.info(f"Editing file: {path}")

        # Record action
        action = ActionRecord(
            action="edit_file",
            path=path,
            parameters={
                "replacements": len(replacements) if replacements else 0,
                "insertions": len(insertions) if insertions else 0
            }
        )

        try:
            full_path = os.path.join(self.agent.workspace_root, path)

            if not os.path.exists(full_path):
                error_msg = f"File not found: {path}"
                action.result = error_msg
                if self.agent.storage:
                    self.agent.storage.action_history.append(action)
                return {"error": error_msg}

            # Read current content
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.splitlines()

            # Apply replacements
            if replacements:
                for replacement in replacements:
                    content = self._apply_replacement(content, lines, replacement)

            # Apply insertions
            if insertions:
                for insertion in insertions:
                    content = self._apply_insertion(content, insertion)

            # Write back to file
            with open(full_path, 'w', encoding='utf-8') as f:
                f.write(content)

            action.result = "File edited successfully"
            if self.agent.storage:
                self.agent.storage.action_history.append(action)

            return {
                "success": True,
                "path": path,
                "operations": {
                    "replacements": len(replacements) if replacements else 0,
                    "insertions": len(insertions) if insertions else 0
                }
            }

        except Exception as e:
            error_msg = f"Error editing {path}: {str(e)}"
            self.logger.error(error_msg)
            action.result = error_msg
            if self.agent.storage:
                self.agent.storage.action_history.append(action)
            return {"error": error_msg}

    def _apply_replacement(self, content: str, lines: List[str],
                          replacement: Dict[str, Any]) -> str:
        """Apply string replacement following str-replace-editor specification"""
        old_str = replacement.get("old_str")
        new_str = replacement.get("new_str", "")
        start_line = replacement.get("start_line", 1) - 1  # Convert to 0-based
        end_line = replacement.get("end_line", len(lines))

        # Extract the section to replace
        target_lines = lines[start_line:end_line]
        target_str = '\n'.join(target_lines)

        if old_str in target_str:
            new_target = target_str.replace(old_str, new_str)
            # Reconstruct full content
            new_lines = lines[:start_line] + new_target.split('\n') + lines[end_line:]
            return '\n'.join(new_lines)

        return content

    def _apply_insertion(self, content: str, insertion: Dict[str, Any]) -> str:
        """Apply insertion following str-replace-editor specification"""
        insert_line = insertion.get("insert_line", 0)
        new_str = insertion.get("new_str", "")

        lines = content.split('\n')

        # Insert at specified line
        if insert_line == 0:
            lines.insert(0, new_str)
        else:
            lines.insert(insert_line, new_str)

        return '\n'.join(lines)

    def create_file(self, path: str, content: str) -> Dict[str, Any]:
        """
        Create new files using save-file specification

        Args:
            path: File path relative to workspace root
            content: File content (limited to 300 lines as per Augment spec)

        Returns:
            Dict containing creation results
        """
        self.logger.info(f"Creating file: {path}")

        # Record action
        action = ActionRecord(
            action="create_file",
            path=path,
            parameters={"content_lines": len(content.split('\n'))}
        )

        try:
            full_path = os.path.join(self.agent.workspace_root, path)

            # Check if file already exists
            if os.path.exists(full_path):
                error_msg = f"File already exists: {path}. Use edit_file instead."
                action.result = error_msg
                if self.agent.storage:
                    self.agent.storage.action_history.append(action)
                return {"error": error_msg}

            # Validate content length (300 line limit per Augment spec)
            lines = content.split('\n')
            if len(lines) > 300:
                error_msg = f"Content exceeds 300 line limit: {len(lines)} lines"
                action.result = error_msg
                if self.agent.storage:
                    self.agent.storage.action_history.append(action)
                return {"error": error_msg}

            # Create directory if needed
            os.makedirs(os.path.dirname(full_path), exist_ok=True)

            # Write file
            with open(full_path, 'w', encoding='utf-8') as f:
                f.write(content)

            action.result = f"File created successfully: {len(lines)} lines"
            if self.agent.storage:
                self.agent.storage.action_history.append(action)

            return {
                "success": True,
                "path": path,
                "lines": len(lines)
            }

        except Exception as e:
            error_msg = f"Error creating {path}: {str(e)}"
            self.logger.error(error_msg)
            action.result = error_msg
            if self.agent.storage:
                self.agent.storage.action_history.append(action)
            return {"error": error_msg}


class ContextGatheringUtilities:
    """
    Context Gathering Utilities following Augment's specifications
    Implements codebase-retrieval and git-commit-retrieval functionality
    """

    def __init__(self, agent: AIIDEAgent):
        self.agent = agent
        self.logger = agent.logger

    def gather_context(self, information_request: str) -> Dict[str, Any]:
        """
        Get codebase context using natural language queries
        Simulates Augment's proprietary retrieval/embedding model

        Args:
            information_request: Natural language description of needed code

        Returns:
            Dict containing retrieved context information
        """
        self.logger.info(f"Gathering codebase context: {information_request[:100]}...")

        # Record action
        action = ActionRecord(
            action="codebase_retrieval",
            parameters={"information_request": information_request}
        )

        try:
            # In a real implementation, this would call Augment's context engine
            # For now, we'll simulate intelligent context gathering
            context_result = self._simulate_codebase_retrieval(information_request)

            action.result = f"Retrieved {len(context_result.get('matches', []))} relevant code snippets"
            if self.agent.storage:
                self.agent.storage.action_history.append(action)
                # Update shared storage with context
                self.agent.storage.context_data["codebase_info"] = context_result

            return context_result

        except Exception as e:
            error_msg = f"Error gathering context: {str(e)}"
            self.logger.error(error_msg)
            action.result = error_msg
            if self.agent.storage:
                self.agent.storage.action_history.append(action)
            return {"error": error_msg}

    def _simulate_codebase_retrieval(self, information_request: str) -> Dict[str, Any]:
        """
        Simulate Augment's codebase retrieval functionality
        In real implementation, this would use proprietary embedding models
        """
        # Analyze the workspace for relevant files
        relevant_files = []
        code_snippets = []

        # Walk through workspace to find relevant files
        for root, dirs, files in os.walk(self.agent.workspace_root):
            # Skip hidden directories
            dirs[:] = [d for d in dirs if not d.startswith('.')]

            for file in files:
                if file.endswith(('.py', '.js', '.ts', '.java', '.cpp', '.c', '.h', '.md')):
                    file_path = os.path.join(root, file)
                    rel_path = os.path.relpath(file_path, self.agent.workspace_root)
                    relevant_files.append(rel_path)

                    # Extract code snippets based on request
                    snippets = self._extract_relevant_snippets(file_path, information_request)
                    code_snippets.extend(snippets)

        return {
            "query": information_request,
            "relevant_files": relevant_files,
            "code_snippets": code_snippets,
            "matches": code_snippets,  # Keep as list for len() compatibility
            "match_count": len(code_snippets),
            "timestamp": ActionRecord(action="timestamp").timestamp
        }

    def _extract_relevant_snippets(self, file_path: str, request: str) -> List[Dict[str, Any]]:
        """Extract relevant code snippets from a file based on the request"""
        snippets = []

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # Simple keyword matching (in real implementation, use semantic search)
            keywords = request.lower().split()

            for i, line in enumerate(lines):
                line_lower = line.lower()
                if any(keyword in line_lower for keyword in keywords):
                    # Extract context around the match
                    start = max(0, i - 2)
                    end = min(len(lines), i + 3)
                    context_lines = lines[start:end]

                    snippet = {
                        "file": os.path.relpath(file_path, self.agent.workspace_root),
                        "line_number": i + 1,
                        "content": ''.join(context_lines),
                        "match_line": line.strip()
                    }
                    snippets.append(snippet)

        except Exception as e:
            self.logger.warning(f"Could not extract snippets from {file_path}: {e}")

        return snippets

    def get_historical_context(self, information_request: str) -> Dict[str, Any]:
        """
        Get git history context for implementation patterns
        Simulates Augment's git-commit-retrieval functionality

        Args:
            information_request: Description of historical information needed

        Returns:
            Dict containing relevant historical context
        """
        self.logger.info(f"Gathering git history context: {information_request[:100]}...")

        # Record action
        action = ActionRecord(
            action="git_commit_retrieval",
            parameters={"information_request": information_request}
        )

        try:
            # In real implementation, this would access git commit history
            # For now, we'll simulate historical context gathering
            history_result = self._simulate_git_history_retrieval(information_request)

            action.result = f"Retrieved {len(history_result.get('commits', []))} relevant commits"
            if self.agent.storage:
                self.agent.storage.action_history.append(action)
                # Update shared storage with historical context
                self.agent.storage.context_data["git_history"] = history_result

            return history_result

        except Exception as e:
            error_msg = f"Error gathering git history: {str(e)}"
            self.logger.error(error_msg)
            action.result = error_msg
            if self.agent.storage:
                self.agent.storage.action_history.append(action)
            return {"error": error_msg}

    def _simulate_git_history_retrieval(self, information_request: str) -> Dict[str, Any]:
        """
        Simulate git commit history retrieval
        In real implementation, this would analyze actual git history
        """
        # Try to get actual git history if available
        commits = []

        try:
            import subprocess

            # Get recent commits
            result = subprocess.run(
                ['git', 'log', '--oneline', '-10'],
                cwd=self.agent.workspace_root,
                capture_output=True,
                text=True
            )

            if result.returncode == 0:
                commit_lines = result.stdout.strip().split('\n')
                for line in commit_lines:
                    if line.strip():
                        parts = line.split(' ', 1)
                        if len(parts) == 2:
                            commits.append({
                                "hash": parts[0],
                                "message": parts[1],
                                "relevance": self._calculate_relevance(parts[1], information_request)
                            })

        except Exception as e:
            self.logger.info(f"Git not available or error: {e}")
            # Provide simulated historical context
            commits = [
                {
                    "hash": "abc123",
                    "message": "Initial implementation of core features",
                    "relevance": 0.8
                },
                {
                    "hash": "def456",
                    "message": "Added file operation utilities",
                    "relevance": 0.6
                }
            ]

        return {
            "query": information_request,
            "commits": commits,
            "total_commits": len(commits),
            "timestamp": ActionRecord(action="timestamp").timestamp
        }

    def _calculate_relevance(self, commit_message: str, request: str) -> float:
        """Calculate relevance score between commit message and request"""
        message_words = set(commit_message.lower().split())
        request_words = set(request.lower().split())

        if not request_words:
            return 0.0

        intersection = message_words.intersection(request_words)
        return len(intersection) / len(request_words)

    def gather_comprehensive_context(self, information_request: str) -> Dict[str, Any]:
        """
        Gather comprehensive context combining codebase and git history
        This is the main method to call before making any edits (CRITICAL)
        """
        self.logger.info("Gathering comprehensive context (CRITICAL before edits)")

        # Gather both types of context
        codebase_context = self.gather_context(information_request)
        git_context = self.get_historical_context(information_request)

        # Combine contexts
        comprehensive_context = {
            "request": information_request,
            "codebase": codebase_context,
            "git_history": git_context,
            "gathered_at": ActionRecord(action="timestamp").timestamp,
            "ready_for_edits": True
        }

        # Update shared storage
        if self.agent.storage:
            self.agent.storage.context_data.update({
                "comprehensive_context": comprehensive_context,
                "context_gathering_complete": True
            })

        return comprehensive_context


class ProcessManagementUtilities:
    """
    Process Management Utilities following Augment's specifications
    Implements safe command execution with timeout controls
    """

    def __init__(self, agent: AIIDEAgent):
        self.agent = agent
        self.logger = agent.logger

    def run_command(self, command: str, working_dir: Optional[str] = None,
                   wait: bool = True, timeout: int = 30) -> Dict[str, Any]:
        """
        Execute commands safely following launch-process specification

        Args:
            command: Shell command to execute
            working_dir: Working directory (defaults to workspace root)
            wait: Whether to wait for command completion
            timeout: Timeout in seconds for waiting commands

        Returns:
            Dict containing execution results
        """
        if working_dir is None:
            working_dir = self.agent.workspace_root

        self.logger.info(f"Executing command: {command}")

        # Record action
        action = ActionRecord(
            action="run_command",
            parameters={
                "command": command,
                "working_dir": working_dir,
                "wait": wait,
                "timeout": timeout
            }
        )

        try:
            import subprocess

            if wait:
                # Execute and wait for completion
                result = subprocess.run(
                    command,
                    shell=True,
                    cwd=working_dir,
                    capture_output=True,
                    text=True,
                    timeout=timeout
                )

                execution_result = {
                    "success": result.returncode == 0,
                    "return_code": result.returncode,
                    "stdout": result.stdout,
                    "stderr": result.stderr,
                    "command": command,
                    "working_dir": working_dir
                }

                action.result = f"Command completed with return code {result.returncode}"

            else:
                # Execute in background (non-waiting)
                process = subprocess.Popen(
                    command,
                    shell=True,
                    cwd=working_dir,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )

                execution_result = {
                    "success": True,
                    "process_id": process.pid,
                    "command": command,
                    "working_dir": working_dir,
                    "background": True
                }

                action.result = f"Command started in background with PID {process.pid}"

            if self.agent.storage:
                self.agent.storage.action_history.append(action)
            return execution_result

        except subprocess.TimeoutExpired:
            error_msg = f"Command timed out after {timeout} seconds: {command}"
            self.logger.error(error_msg)
            action.result = error_msg
            if self.agent.storage:
                self.agent.storage.action_history.append(action)
            return {"error": error_msg, "timeout": True}

        except Exception as e:
            error_msg = f"Error executing command '{command}': {str(e)}"
            self.logger.error(error_msg)
            action.result = error_msg
            if self.agent.storage:
                self.agent.storage.action_history.append(action)
            return {"error": error_msg}

    def run_package_manager_command(self, package_manager: str, operation: str,
                                   packages: List[str] = None) -> Dict[str, Any]:
        """
        Execute package manager commands safely
        Follows Augment's rule of using package managers instead of manual edits

        Args:
            package_manager: npm, pip, cargo, etc.
            operation: install, uninstall, add, remove, etc.
            packages: List of package names

        Returns:
            Dict containing execution results
        """
        if packages is None:
            packages = []

        self.logger.info(f"Running {package_manager} {operation} for packages: {packages}")

        # Build command based on package manager
        command_map = {
            "npm": {
                "install": f"npm install {' '.join(packages)}",
                "uninstall": f"npm uninstall {' '.join(packages)}"
            },
            "pip": {
                "install": f"pip install {' '.join(packages)}",
                "uninstall": f"pip uninstall -y {' '.join(packages)}"
            },
            "cargo": {
                "add": f"cargo add {' '.join(packages)}",
                "remove": f"cargo remove {' '.join(packages)}"
            },
            "yarn": {
                "add": f"yarn add {' '.join(packages)}",
                "remove": f"yarn remove {' '.join(packages)}"
            }
        }

        if package_manager not in command_map:
            error_msg = f"Unsupported package manager: {package_manager}"
            return {"error": error_msg}

        if operation not in command_map[package_manager]:
            error_msg = f"Unsupported operation '{operation}' for {package_manager}"
            return {"error": error_msg}

        command = command_map[package_manager][operation]

        # Execute with longer timeout for package operations
        return self.run_command(command, timeout=300)

    def run_tests(self, test_command: Optional[str] = None,
                  test_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Run tests following Augment's test-driven development approach

        Args:
            test_command: Specific test command to run
            test_path: Path to specific test file or directory

        Returns:
            Dict containing test results
        """
        self.logger.info("Running tests...")

        # Auto-detect test command if not provided
        if test_command is None:
            test_command = self._detect_test_command()

        if test_path:
            test_command = f"{test_command} {test_path}"

        # Record action
        action = ActionRecord(
            action="run_tests",
            parameters={
                "test_command": test_command,
                "test_path": test_path
            }
        )

        try:
            result = self.run_command(test_command, timeout=120)

            # Analyze test results
            test_result = self._analyze_test_output(result)

            action.result = f"Tests completed: {test_result.get('summary', 'Unknown')}"
            if self.agent.storage:
                self.agent.storage.action_history.append(action)

            return test_result

        except Exception as e:
            error_msg = f"Error running tests: {str(e)}"
            self.logger.error(error_msg)
            action.result = error_msg
            if self.agent.storage:
                self.agent.storage.action_history.append(action)
            return {"error": error_msg}

    def _detect_test_command(self) -> str:
        """Auto-detect appropriate test command based on project structure"""
        workspace = self.agent.workspace_root

        # Check for common test configurations
        if os.path.exists(os.path.join(workspace, "package.json")):
            return "npm test"
        elif os.path.exists(os.path.join(workspace, "pytest.ini")) or \
             os.path.exists(os.path.join(workspace, "setup.py")):
            return "pytest"
        elif os.path.exists(os.path.join(workspace, "Cargo.toml")):
            return "cargo test"
        elif os.path.exists(os.path.join(workspace, "pom.xml")):
            return "mvn test"
        else:
            # Default to Python unittest
            return "python -m unittest discover"

    def _analyze_test_output(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze test output to extract meaningful information"""
        if "error" in result:
            return result

        stdout = result.get("stdout", "")
        stderr = result.get("stderr", "")
        return_code = result.get("return_code", 0)

        test_analysis = {
            "success": return_code == 0,
            "return_code": return_code,
            "output": stdout,
            "errors": stderr,
            "summary": "Tests passed" if return_code == 0 else "Tests failed"
        }

        # Extract test counts if possible
        if "passed" in stdout.lower() or "failed" in stdout.lower():
            test_analysis["detailed_results"] = self._extract_test_counts(stdout)

        return test_analysis

    def _extract_test_counts(self, output: str) -> Dict[str, int]:
        """Extract test counts from test output"""
        import re

        counts = {"passed": 0, "failed": 0, "skipped": 0}

        # Common patterns for test results
        patterns = [
            r"(\d+) passed",
            r"(\d+) failed",
            r"(\d+) skipped",
            r"(\d+) error"
        ]

        for pattern in patterns:
            matches = re.findall(pattern, output.lower())
            if matches:
                key = pattern.split()[1].rstrip("s")  # Remove 's' from plural
                counts[key] = int(matches[0])

        return counts


if __name__ == "__main__":
    # Example usage
    workspace = os.getcwd()
    agent = AIIDEAgent(workspace)

    # Test with a simple query
    response = agent.process_request("Create a simple Python function")
    print(response)
