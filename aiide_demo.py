#!/usr/bin/env python3
"""
AIIDE Demo Script
Demonstrates the capabilities of the AIIDE agent following Augment's blueprint
"""

import os
import json
from aiide_agent import AIIDEAgent, TaskState, AgentState


def demo_basic_functionality():
    """Demonstrate basic AIIDE functionality"""
    print("=" * 60)
    print("AIIDE - Augment-Inspired Intelligent Development Environment")
    print("=" * 60)
    print()
    
    # Initialize AIIDE agent
    workspace = os.getcwd()
    agent = AIIDEAgent(workspace)
    
    print(f"Workspace: {workspace}")
    print(f"Augment instructions loaded: {agent.augment_instructions['loaded']}")
    print()
    
    # Test 1: File creation request
    print("Test 1: File Creation Request")
    print("-" * 30)
    response = agent.process_request("Create a simple Python function that calculates fibonacci numbers")
    print(response)
    print()
    
    # Show agent status
    status = agent.get_status()
    print("Agent Status:")
    print(json.dumps(status, indent=2))
    print()
    
    return agent


def demo_context_gathering(agent):
    """Demonstrate context gathering capabilities"""
    print("Test 2: Context Gathering Demonstration")
    print("-" * 40)
    
    # Test context gathering utilities directly
    context_result = agent.context_ops.gather_context("Python functions and file operations")
    print(f"Context gathering result: {len(context_result.get('code_snippets', []))} snippets found")
    
    # Test git history gathering
    git_result = agent.context_ops.get_historical_context("file creation and editing")
    print(f"Git history result: {len(git_result.get('commits', []))} commits analyzed")
    print()


def demo_file_operations(agent):
    """Demonstrate file operation capabilities"""
    print("Test 3: File Operations Demonstration")
    print("-" * 40)
    
    # Test viewing current directory
    view_result = agent.file_ops.view_code(".", None, None)
    print(f"Directory view: {len(view_result.get('files', []))} items found")
    
    # Test viewing the AIIDE agent file itself
    view_result = agent.file_ops.view_code("aiide_agent.py", [1, 20], None)
    print(f"File view result: {view_result.get('line_count', 0)} total lines")
    
    # Test creating a sample file
    sample_content = '''def fibonacci(n):
    """Calculate fibonacci number using recursion"""
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

def fibonacci_iterative(n):
    """Calculate fibonacci number using iteration"""
    if n <= 1:
        return n
    
    a, b = 0, 1
    for _ in range(2, n + 1):
        a, b = b, a + b
    return b

if __name__ == "__main__":
    # Test both implementations
    for i in range(10):
        print(f"fib({i}) = {fibonacci(i)} (recursive)")
        print(f"fib({i}) = {fibonacci_iterative(i)} (iterative)")
'''
    
    create_result = agent.file_ops.create_file("fibonacci_demo.py", sample_content)
    print(f"File creation result: {create_result}")
    print()


def demo_process_management(agent):
    """Demonstrate process management capabilities"""
    print("Test 4: Process Management Demonstration")
    print("-" * 40)
    
    # Test running a simple command
    result = agent.process_ops.run_command("python --version")
    print(f"Python version check: {result.get('success', False)}")
    if result.get('stdout'):
        print(f"Output: {result['stdout'].strip()}")
    
    # Test running the created fibonacci script
    if os.path.exists("fibonacci_demo.py"):
        result = agent.process_ops.run_command("python fibonacci_demo.py")
        print(f"Fibonacci script execution: {result.get('success', False)}")
        if result.get('stdout'):
            print("Output preview:")
            lines = result['stdout'].strip().split('\n')[:5]  # Show first 5 lines
            for line in lines:
                print(f"  {line}")
            if len(result['stdout'].strip().split('\n')) > 5:
                print("  ...")
    print()


def demo_code_display(agent):
    """Demonstrate Augment-style code display"""
    print("Test 5: Code Display Demonstration")
    print("-" * 40)
    
    # Read and display code using Augment format
    if os.path.exists("fibonacci_demo.py"):
        with open("fibonacci_demo.py", 'r') as f:
            content = f.read()
        
        # Display using Augment's format
        display = agent.display_code_snippet("fibonacci_demo.py", content, max_lines=10)
        print("Code display using Augment format:")
        print(display)
    print()


def demo_task_management(agent):
    """Demonstrate task management capabilities"""
    print("Test 6: Task Management Demonstration")
    print("-" * 40)
    
    # Process a complex request that creates multiple tasks
    response = agent.process_request("Create a comprehensive Python module with functions, tests, and documentation")
    print("Complex task response:")
    print(response)
    
    # Show detailed task breakdown
    status = agent.get_status()
    print("\nDetailed task breakdown:")
    for task in status.get('tasks', []):
        print(f"  {task['state']} {task['name']}: {task['description']}")
    print()


def demo_error_handling():
    """Demonstrate error handling and recovery"""
    print("Test 7: Error Handling Demonstration")
    print("-" * 40)
    
    workspace = os.getcwd()
    agent = AIIDEAgent(workspace)
    
    # Test with invalid file operation
    result = agent.file_ops.view_code("nonexistent_file.py")
    print(f"Invalid file access handled: {'error' in result}")
    
    # Test with invalid command
    result = agent.process_ops.run_command("invalid_command_that_does_not_exist")
    print(f"Invalid command handled: {'error' in result}")
    print()


def main():
    """Main demo function"""
    try:
        # Run all demonstrations
        agent = demo_basic_functionality()
        demo_context_gathering(agent)
        demo_file_operations(agent)
        demo_process_management(agent)
        demo_code_display(agent)
        demo_task_management(agent)
        demo_error_handling()
        
        print("=" * 60)
        print("AIIDE Demo Completed Successfully!")
        print("=" * 60)
        print()
        print("AIIDE Features Demonstrated:")
        print("✓ Context-first approach with mandatory context gathering")
        print("✓ File operations (view, create, edit) with safety features")
        print("✓ Process management with timeout controls")
        print("✓ Task management for complex workflows")
        print("✓ Augment-style code display formatting")
        print("✓ Error handling and recovery")
        print("✓ Conservative approach respecting existing patterns")
        print()
        print("AIIDE is ready for Augment-like coding assistance!")
        
    except Exception as e:
        print(f"Demo encountered an error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
