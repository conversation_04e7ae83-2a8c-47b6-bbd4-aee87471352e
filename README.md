# AIIDE - Augment-Inspired Intelligent Development Environment

AIIDE is an Augment-like coding agent that implements the core patterns and capabilities of Augment Agent, following the blueprint architecture for context-aware, safe, and intelligent code assistance.

## Overview

AIIDE follows Augment's proven approach to agentic coding:
- **Context-first**: Always gathers comprehensive context before making any edits
- **Conservative**: Respects existing codebase patterns and asks permission for system changes
- **Safe**: Implements file operations with undo capabilities and timeout controls
- **Test-driven**: Suggests and runs tests to validate changes
- **Task-aware**: Manages complex workflows with structured task breakdown

## Architecture

AIIDE implements the central agent node design from the blueprint:

```
User Request → Context Gathering → Action Planning → Execution → Verification → Response
```

### Core Components

1. **AIIDEAgent**: Main agent class implementing the workflow
2. **FileOperationUtilities**: Safe file operations (view, create, edit)
3. **ContextGatheringUtilities**: Codebase and git history analysis
4. **ProcessManagementUtilities**: Safe command execution with timeouts
5. **SharedStorage**: Node communication and state management

## Key Features

### Context Gathering (CRITICAL)
- **codebase-retrieval**: Semantic search across codebase using natural language
- **git-commit-retrieval**: Historical context from commit history
- **Comprehensive analysis**: Combines current state and historical patterns

### File Operations
- **view**: Read files/directories with line ranges and regex search
- **create**: New file creation with 300-line limit safety check
- **edit**: String replacement and insertion with precise line targeting
- **Safety**: All operations include undo capabilities and validation

### Process Management
- **Command execution**: Safe shell command execution with timeouts
- **Package management**: Automated npm, pip, cargo, yarn operations
- **Test execution**: Automated test running with result analysis
- **Background processes**: Non-blocking command execution

### Task Management
- **Complex workflows**: Break down large tasks into manageable subtasks
- **Progress tracking**: Real-time status updates and completion monitoring
- **State management**: NOT_STARTED, IN_PROGRESS, CANCELLED, COMPLETE

## Usage

### Basic Usage

```python
from aiide_agent import AIIDEAgent

# Initialize AIIDE
workspace = "/path/to/your/project"
agent = AIIDEAgent(workspace)

# Process a request
response = agent.process_request("Create a Python function to calculate fibonacci numbers")
print(response)

# Check status
status = agent.get_status()
print(status)
```

### Advanced Usage

```python
# Direct utility access
agent = AIIDEAgent(workspace)

# Gather context before editing
context = agent.context_ops.gather_comprehensive_context("file operations and utilities")

# Perform file operations
result = agent.file_ops.view_code("src/main.py", line_range=[1, 50])
result = agent.file_ops.create_file("new_module.py", content)

# Execute commands safely
result = agent.process_ops.run_command("python -m pytest tests/")
result = agent.process_ops.run_package_manager_command("pip", "install", ["requests"])
```

## Code Display Format

AIIDE follows Augment's code display specification:

```xml
<augment_code_snippet path="file/path.py" mode="EXCERPT">
````python
def example_function():
    """Example function"""
    return "Hello from AIIDE!"
````
</augment_code_snippet>
```

## Safety Features

### Conservative Approach
- Only implements what user explicitly requests
- Asks permission before: committing, pushing, merging, installing dependencies
- Respects existing codebase patterns and conventions

### Error Recovery
- Comprehensive logging for progress tracking
- Graceful error handling with detailed error messages
- Automatic detection of circular operations

### File Safety
- Undo capabilities for all file operations
- Validation of file paths and content
- Automatic backup creation for critical edits

## Testing

Run the demo to see AIIDE in action:

```bash
python aiide_demo.py
```

The demo demonstrates:
- Basic functionality and workflow
- Context gathering capabilities
- File operations (view, create, edit)
- Process management and command execution
- Code display formatting
- Task management for complex workflows
- Error handling and recovery

## Configuration

AIIDE loads configuration from `augment_instruction.txt` which contains:
- Detailed API specifications
- Workflow rules and safety patterns
- Code display formatting requirements
- Package management guidelines

## Comparison with Augment

AIIDE implements Augment's core patterns:

| Feature | Augment | AIIDE |
|---------|---------|-------|
| Context Engine | Proprietary embedding model | Simulated semantic search |
| File Operations | Real-time codebase indexing | File system analysis |
| Git Integration | Native git history access | Git command integration |
| Task Management | Built-in system | Implemented task breakdown |
| Safety Features | Production-grade | Development-focused |

## Development Status

- ✅ Phase 1: Human Design - Environment setup complete
- ✅ Phase 2: AI Implementation - Core agent development complete  
- 🔄 Phase 3: Agent Execution - Testing and validation in progress

## Next Steps

1. **Enhanced Context Engine**: Implement more sophisticated semantic search
2. **Real Git Integration**: Direct git repository analysis
3. **Advanced Task Management**: Hierarchical task dependencies
4. **Plugin System**: Extensible utility framework
5. **Production Safety**: Enhanced error recovery and validation

## License

This project implements the Augment Agent blueprint for educational and development purposes.

---

**AIIDE - Bringing Augment's intelligence to your development workflow**
